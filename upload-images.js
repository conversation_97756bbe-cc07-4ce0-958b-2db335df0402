#!/usr/bin/env node

import { promises as fs } from 'fs';
import path from 'path';
import { URL } from 'url';
import axios from 'axios';
import ExcelJS from 'exceljs';
import Bottleneck from 'bottleneck';

// Configuration constants
const CONFIG = {
  EXCEL_FILE_PATH: './images.xlsx',
  LIGHTSPEED_API_TOKEN: 'lsxs_pt_qBAiIf8cgIBCrhbCNsP4MuLW4g4sL12q',
  STORE_DOMAIN: 'eaglemarketing020.vendhq.com',
  RATE_LIMIT_PER_MINUTE: 100,
  MAX_RETRIES: 3,
  RETRY_BASE_DELAY_MS: 1000,
  RETRY_MULTIPLIER: 2,
  DOWNLOAD_TIMEOUT_MS: 30000,
  UPLOAD_TIMEOUT_MS: 60000,
  CHECKPOINT_FILE: './upload_checkpoint.json'
};

// Rate limiter setup
const limiter = new Bottleneck({
  reservoir: CONFIG.RATE_LIMIT_PER_MINUTE,
  reservoirRefreshAmount: CONFIG.RATE_LIMIT_PER_MINUTE,
  reservoirRefreshInterval: 60 * 1000, // 1 minute
  maxConcurrent: 5
});

// Global state
let checkpoint = {
  successful: new Set(),
  failed: [],
  lastUpdated: null
};

let stats = {
  totalProducts: 0,
  totalImages: 0,
  successfulUploads: 0,
  failedUploads: 0,
  startTime: null,
  failureReasons: {}
};

/**
 * Logs a message with timestamp
 * @param {string} message - The message to log
 */
function log(message) {
  console.log(`[${new Date().toISOString()}] ${message}`);
}

/**
 * Loads checkpoint data from file
 * @returns {Promise<void>}
 */
async function loadCheckpoint() {
  try {
    const data = await fs.readFile(CONFIG.CHECKPOINT_FILE, 'utf8');
    const parsed = JSON.parse(data);
    checkpoint.successful = new Set(parsed.successful || []);
    checkpoint.failed = parsed.failed || [];
    checkpoint.lastUpdated = parsed.lastUpdated;
    log(`Loaded checkpoint with ${checkpoint.successful.size} successful uploads`);
  } catch (error) {
    if (error.code !== 'ENOENT') {
      log(`Warning: Could not load checkpoint: ${error.message}`);
    }
  }
}

/**
 * Saves checkpoint data to file
 * @returns {Promise<void>}
 */
async function saveCheckpoint() {
  try {
    const data = {
      successful: Array.from(checkpoint.successful),
      failed: checkpoint.failed,
      lastUpdated: new Date().toISOString()
    };
    await fs.writeFile(CONFIG.CHECKPOINT_FILE, JSON.stringify(data, null, 2));
  } catch (error) {
    log(`Warning: Could not save checkpoint: ${error.message}`);
  }
}

/**
 * Validates if a URL is accessible
 * @param {string} url - The URL to validate
 * @returns {Promise<boolean>}
 */
async function validateUrl(url) {
  try {
    new URL(url);
    const response = await axios.head(url, {
      timeout: 5000,
      validateStatus: (status) => status < 400
    });
    return response.status < 400;
  } catch (error) {
    return false;
  }
}

/**
 * Downloads an image from URL to temporary file
 * @param {string} imageUrl - The URL of the image to download
 * @returns {Promise<string>} - Path to the temporary file
 */
async function downloadImage(imageUrl) {
  const tempDir = './temp';
  await fs.mkdir(tempDir, { recursive: true });
  
  const urlObj = new URL(imageUrl);
  const extension = path.extname(urlObj.pathname) || '.jpg';
  const tempFilePath = path.join(tempDir, `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}${extension}`);
  
  const response = await axios({
    method: 'GET',
    url: imageUrl,
    responseType: 'stream',
    timeout: CONFIG.DOWNLOAD_TIMEOUT_MS
  });
  
  const writer = (await import('fs')).createWriteStream(tempFilePath);
  response.data.pipe(writer);
  
  return new Promise((resolve, reject) => {
    writer.on('finish', () => resolve(tempFilePath));
    writer.on('error', reject);
  });
}

/**
 * Uploads an image file to Lightspeed API
 * @param {string} productId - The product ID
 * @param {string} filePath - Path to the image file
 * @returns {Promise<void>}
 */
async function uploadImage(productId, filePath) {
  const FormData = (await import('form-data')).default;
  const form = new FormData();
  
  const fileStream = (await import('fs')).createReadStream(filePath);
  form.append('image', fileStream);
  
  const url = `https://${CONFIG.STORE_DOMAIN}/api/2.0/products/${productId}/actions/image_upload`;
  
  await axios.post(url, form, {
    headers: {
      ...form.getHeaders(),
      'Authorization': `Bearer ${CONFIG.LIGHTSPEED_API_TOKEN}`
    },
    timeout: CONFIG.UPLOAD_TIMEOUT_MS
  });
}

/**
 * Processes a single image with retry logic
 * @param {string} productId - The product ID
 * @param {string} imageUrl - The image URL
 * @returns {Promise<boolean>} - Success status
 */
async function processImage(productId, imageUrl) {
  const key = `${productId}:${imageUrl}`;
  
  if (checkpoint.successful.has(key)) {
    log(`Skipping already uploaded image for product ${productId}`);
    return true;
  }
  
  let lastError = null;
  
  for (let attempt = 1; attempt <= CONFIG.MAX_RETRIES; attempt++) {
    let tempFilePath = null;
    
    try {
      log(`Downloading image for product ${productId} (attempt ${attempt}/${CONFIG.MAX_RETRIES})`);
      tempFilePath = await downloadImage(imageUrl);
      
      log(`Uploading image for product ${productId} (attempt ${attempt}/${CONFIG.MAX_RETRIES})`);
      await limiter.schedule(() => uploadImage(productId, tempFilePath));
      
      checkpoint.successful.add(key);
      await saveCheckpoint();
      
      log(`✓ Successfully uploaded image for product ${productId}`);
      stats.successfulUploads++;
      return true;
      
    } catch (error) {
      lastError = error;
      const errorMsg = error.response?.status === 429 ? 'Rate limited' : 
                      error.code === 'ECONNABORTED' ? 'Timeout' :
                      error.response?.status >= 500 ? 'Server error' :
                      error.message;
      
      log(`✗ Failed to process image for product ${productId} (attempt ${attempt}): ${errorMsg}`);
      
      if (attempt < CONFIG.MAX_RETRIES) {
        const delay = CONFIG.RETRY_BASE_DELAY_MS * Math.pow(CONFIG.RETRY_MULTIPLIER, attempt - 1);
        log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    } finally {
      if (tempFilePath) {
        try {
          await fs.unlink(tempFilePath);
        } catch (error) {
          log(`Warning: Could not delete temp file ${tempFilePath}: ${error.message}`);
        }
      }
    }
  }
  
  // Record failure
  const failureReason = lastError?.response?.status === 429 ? 'Rate limited' :
                       lastError?.code === 'ECONNABORTED' ? 'Timeout' :
                       lastError?.response?.status >= 500 ? 'Server error' :
                       lastError?.response?.status === 401 ? 'Authentication failed' :
                       lastError?.response?.status === 404 ? 'Product not found' :
                       'Network/Unknown error';
  
  checkpoint.failed.push({
    productId,
    imageUrl,
    error: failureReason,
    timestamp: new Date().toISOString()
  });
  
  stats.failedUploads++;
  stats.failureReasons[failureReason] = (stats.failureReasons[failureReason] || 0) + 1;
  
  await saveCheckpoint();
  return false;
}

/**
 * Reads and parses the Excel file
 * @returns {Promise<Map<string, string[]>>} - Map of product_id to array of image URLs
 */
async function readExcelFile() {
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.readFile(CONFIG.EXCEL_FILE_PATH);

  const worksheet = workbook.getWorksheet(0);
  if (!worksheet) {
    throw new Error('No worksheet found in Excel file');
  }

  const productImages = new Map();
  let rowCount = 0;
  let hasHeader = false;

  worksheet.eachRow((row, rowNumber) => {
    if (rowNumber === 1) {
      // Check if first row looks like a header
      const firstCell = row.getCell(1).value;
      const secondCell = row.getCell(2).value;
      if (typeof firstCell === 'string' && typeof secondCell === 'string' &&
          (firstCell.toLowerCase().includes('product') || firstCell.toLowerCase().includes('id')) &&
          (secondCell.toLowerCase().includes('image') || secondCell.toLowerCase().includes('url'))) {
        hasHeader = true;
        return;
      }
    }

    if (hasHeader && rowNumber === 1) return;

    const productId = row.getCell(1).value?.toString().trim();
    const imageUrl = row.getCell(2).value?.toString().trim();

    if (!productId || !imageUrl) {
      log(`Warning: Skipping row ${rowNumber} - missing product_id or image_url`);
      return;
    }

    if (!productImages.has(productId)) {
      productImages.set(productId, []);
    }

    productImages.get(productId).push(imageUrl);
    rowCount++;
  });

  log(`Loaded ${rowCount} image entries for ${productImages.size} products`);
  return productImages;
}

/**
 * Processes all products and their images
 * @param {Map<string, string[]>} productImages - Map of product_id to image URLs
 * @returns {Promise<void>}
 */
async function processAllProducts(productImages) {
  stats.totalProducts = productImages.size;
  stats.totalImages = Array.from(productImages.values()).reduce((sum, urls) => sum + urls.length, 0);

  log(`Starting processing of ${stats.totalProducts} products with ${stats.totalImages} total images`);

  for (const [productId, imageUrls] of productImages) {
    log(`Processing product ${productId} with ${imageUrls.length} images`);

    for (const imageUrl of imageUrls) {
      // Validate URL before processing
      if (!(await validateUrl(imageUrl))) {
        log(`✗ Skipping invalid/inaccessible URL for product ${productId}: ${imageUrl}`);
        stats.failedUploads++;
        stats.failureReasons['Invalid URL'] = (stats.failureReasons['Invalid URL'] || 0) + 1;

        checkpoint.failed.push({
          productId,
          imageUrl,
          error: 'Invalid URL',
          timestamp: new Date().toISOString()
        });
        continue;
      }

      await processImage(productId, imageUrl);
    }

    log(`Completed processing product ${productId}`);
  }
}

/**
 * Generates and displays the final summary report
 * @returns {void}
 */
function generateSummaryReport() {
  const endTime = Date.now();
  const duration = Math.round((endTime - stats.startTime) / 1000);

  log('\n' + '='.repeat(60));
  log('PROCESSING COMPLETE - SUMMARY REPORT');
  log('='.repeat(60));
  log(`Total products processed: ${stats.totalProducts}`);
  log(`Total images attempted: ${stats.totalImages}`);
  log(`Successful uploads: ${stats.successfulUploads}`);
  log(`Failed uploads: ${stats.failedUploads}`);
  log(`Success rate: ${stats.totalImages > 0 ? ((stats.successfulUploads / stats.totalImages) * 100).toFixed(1) : 0}%`);
  log(`Processing duration: ${Math.floor(duration / 60)}m ${duration % 60}s`);

  if (Object.keys(stats.failureReasons).length > 0) {
    log('\nFailure breakdown:');
    for (const [reason, count] of Object.entries(stats.failureReasons)) {
      log(`  ${reason}: ${count}`);
    }
  }

  log('='.repeat(60));
}

/**
 * Main execution function
 * @returns {Promise<void>}
 */
async function main() {
  try {
    stats.startTime = Date.now();
    log('Starting Lightspeed X-Series image upload process');

    // Validate configuration
    if (CONFIG.LIGHTSPEED_API_TOKEN === 'your_token_here' || CONFIG.STORE_DOMAIN === 'your_store.vendhq.com') {
      throw new Error('Please configure LIGHTSPEED_API_TOKEN and STORE_DOMAIN in the CONFIG section');
    }

    // Load checkpoint
    await loadCheckpoint();

    // Read Excel file
    const productImages = await readExcelFile();

    // Process all products
    await processAllProducts(productImages);

    // Generate summary
    generateSummaryReport();

    // Cleanup temp directory
    try {
      await fs.rmdir('./temp', { recursive: true });
    } catch (error) {
      // Ignore cleanup errors
    }

    log('Process completed successfully');
    process.exit(0);

  } catch (error) {
    log(`Fatal error: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  log('Received SIGINT, saving checkpoint and exiting...');
  await saveCheckpoint();
  process.exit(1);
});

process.on('SIGTERM', async () => {
  log('Received SIGTERM, saving checkpoint and exiting...');
  await saveCheckpoint();
  process.exit(1);
});

// Start the process
main().catch(async (error) => {
  log(`Unhandled error: ${error.message}`);
  console.error(error);
  await saveCheckpoint();
  process.exit(1);
});
