#!/usr/bin/env node

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import sql from 'mssql';
import csv from 'fast-csv';

// Configuration constants
const CONFIG = {
  CONTAINER_NAME: 'mssql-migration-temp',
  DOCKER_IMAGE: 'mcr.microsoft.com/mssql/server:2019-latest',
  SA_PASSWORD: 'YourStrong!Passw0rd',
  DATABASE_NAME: 'MigrationDB',
  BACKUP_FILE: './input_data/retailer.bak',
  OUTPUT_DIR: './output_data',
  CONTAINER_READY_TIMEOUT: 60000, // 60 seconds
  CONTAINER_READY_INTERVAL: 2000,  // 2 seconds
  CONNECTION_TIMEOUT: 30000,       // 30 seconds
  REQUEST_TIMEOUT: 60000,          // 60 seconds
  CONNECTION_RETRIES: 3,
  CONNECTION_RETRY_DELAY: 5000     // 5 seconds
};

// Global state
let dockerContainer = null;
let dbConnection = null;
let stats = {
  totalTables: 0,
  totalRows: 0,
  startTime: null,
  processedTables: []
};

/**
 * Logs a message with timestamp
 * @param {string} message - The message to log
 */
function log(message) {
  console.log(`[${new Date().toISOString()}] ${message}`);
}

/**
 * Executes a shell command using spawn and returns a promise
 * @param {string} command - The command to execute
 * @param {string[]} args - Command arguments
 * @param {Object} options - Spawn options
 * @returns {Promise<{stdout: string, stderr: string, code: number}>}
 */
function executeCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, { 
      stdio: ['pipe', 'pipe', 'pipe'],
      ...options 
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout?.on('data', (data) => {
      stdout += data.toString();
    });
    
    child.stderr?.on('data', (data) => {
      stderr += data.toString();
    });
    
    child.on('close', (code) => {
      resolve({ stdout, stderr, code });
    });
    
    child.on('error', (error) => {
      reject(new Error(`Failed to execute ${command}: ${error.message}`));
    });
  });
}

/**
 * Starts the MSSQL Docker container
 * @returns {Promise<void>}
 */
async function startDockerContainer() {
  log('Starting MSSQL Docker container...');
  
  // Check if container already exists and remove it
  try {
    const { code } = await executeCommand('docker', ['rm', '-f', CONFIG.CONTAINER_NAME]);
    if (code === 0) {
      log('Removed existing container');
    }
  } catch (error) {
    // Container doesn't exist, continue
  }
  
  const dockerArgs = [
    'run',
    '--name', CONFIG.CONTAINER_NAME,
    '--rm',
    '-p', '1433:1433',
    '-e', 'ACCEPT_EULA=Y',
    '-e', `SA_PASSWORD=${CONFIG.SA_PASSWORD}`,
    '-v', `${path.resolve('./data')}:/var/opt/mssql/backup:ro`,
    '-d',
    CONFIG.DOCKER_IMAGE
  ];
  
  const result = await executeCommand('docker', dockerArgs);
  
  if (result.code !== 0) {
    throw new Error(`Failed to start Docker container: ${result.stderr}`);
  }
  
  dockerContainer = CONFIG.CONTAINER_NAME;
  log(`Docker container started: ${dockerContainer}`);
}

/**
 * Waits for the MSSQL container to be ready
 * @returns {Promise<void>}
 */
async function waitForContainerReady() {
  log('Waiting for MSSQL container to be ready...');
  
  const startTime = Date.now();
  const timeout = CONFIG.CONTAINER_READY_TIMEOUT;
  
  while (Date.now() - startTime < timeout) {
    try {
      const result = await executeCommand('docker', [
        'exec',
        CONFIG.CONTAINER_NAME,
        '/opt/mssql-tools/bin/sqlcmd',
        '-S', 'localhost',
        '-U', 'sa',
        '-P', CONFIG.SA_PASSWORD,
        '-Q', 'SELECT 1'
      ]);
      
      if (result.code === 0) {
        log('MSSQL container is ready');
        return;
      }
    } catch (error) {
      // Container not ready yet, continue polling
    }
    
    log('Container not ready yet, waiting...');
    await new Promise(resolve => setTimeout(resolve, CONFIG.CONTAINER_READY_INTERVAL));
  }
  
  throw new Error(`Container failed to become ready within ${timeout / 1000} seconds`);
}

/**
 * Restores the database from backup file
 * @returns {Promise<void>}
 */
async function restoreDatabase() {
  log('Restoring database from backup...');
  
  const restoreQuery = `
    RESTORE DATABASE ${CONFIG.DATABASE_NAME} 
    FROM DISK = '/var/opt/mssql/backup/data.bak' 
    WITH MOVE '${CONFIG.DATABASE_NAME}' TO '/var/opt/mssql/data/${CONFIG.DATABASE_NAME}.mdf',
    MOVE '${CONFIG.DATABASE_NAME}_Log' TO '/var/opt/mssql/data/${CONFIG.DATABASE_NAME}_Log.ldf',
    REPLACE
  `;
  
  const result = await executeCommand('docker', [
    'exec',
    CONFIG.CONTAINER_NAME,
    '/opt/mssql-tools/bin/sqlcmd',
    '-S', 'localhost',
    '-U', 'sa',
    '-P', CONFIG.SA_PASSWORD,
    '-Q', restoreQuery
  ]);
  
  if (result.code !== 0) {
    throw new Error(`Database restoration failed: ${result.stderr}`);
  }
  
  log('Database restored successfully');
}

/**
 * Establishes connection to the MSSQL database with retry logic
 * @returns {Promise<void>}
 */
async function connectToDatabase() {
  const config = {
    server: 'localhost',
    port: 1433,
    database: CONFIG.DATABASE_NAME,
    user: 'sa',
    password: CONFIG.SA_PASSWORD,
    options: {
      trustServerCertificate: true,
      connectTimeout: CONFIG.CONNECTION_TIMEOUT,
      requestTimeout: CONFIG.REQUEST_TIMEOUT
    }
  };
  
  let lastError = null;
  
  for (let attempt = 1; attempt <= CONFIG.CONNECTION_RETRIES; attempt++) {
    try {
      log(`Connecting to database (attempt ${attempt}/${CONFIG.CONNECTION_RETRIES})...`);
      
      dbConnection = new sql.ConnectionPool(config);
      await dbConnection.connect();
      
      log('Successfully connected to database');
      return;
      
    } catch (error) {
      lastError = error;
      log(`Connection attempt ${attempt} failed: ${error.message}`);
      
      if (attempt < CONFIG.CONNECTION_RETRIES) {
        log(`Retrying in ${CONFIG.CONNECTION_RETRY_DELAY / 1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, CONFIG.CONNECTION_RETRY_DELAY));
      }
    }
  }
  
  throw new Error(`Failed to connect to database after ${CONFIG.CONNECTION_RETRIES} attempts: ${lastError?.message}`);
}

/**
 * Discovers all user tables in the database
 * @returns {Promise<string[]>} - Array of table names
 */
async function discoverTables() {
  log('Discovering user tables...');

  const query = `
    SELECT TABLE_NAME
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_SCHEMA != 'sys'
    ORDER BY TABLE_NAME
  `;

  const request = new sql.Request(dbConnection);
  const result = await request.query(query);

  const tables = result.recordset.map(row => row.TABLE_NAME);
  log(`Found ${tables.length} user tables: ${tables.join(', ')}`);

  return tables;
}

/**
 * Gets the row count for a specific table
 * @param {string} tableName - The table name
 * @returns {Promise<number>} - Row count
 */
async function getTableRowCount(tableName) {
  const request = new sql.Request(dbConnection);
  const result = await request.query(`SELECT COUNT(*) as row_count FROM [${tableName}]`);
  return result.recordset[0].row_count;
}

/**
 * Exports a single table to CSV
 * @param {string} tableName - The table name to export
 * @returns {Promise<void>}
 */
async function exportTableToCSV(tableName) {
  log(`Processing table: ${tableName}`);

  try {
    // Get row count
    const rowCount = await getTableRowCount(tableName);
    log(`Table ${tableName}: ${rowCount} rows`);

    // Create output directory
    const outputDir = path.join(CONFIG.OUTPUT_DIR, tableName);
    await fs.mkdir(outputDir, { recursive: true });

    const csvFilePath = path.join(outputDir, `${tableName}.csv`);

    // Create CSV write stream
    const csvStream = csv.format({ headers: true, encoding: 'utf8' });
    const writeStream = (await import('fs')).createWriteStream(csvFilePath, { encoding: 'utf8' });
    csvStream.pipe(writeStream);

    if (rowCount === 0) {
      // Handle empty table - create CSV with headers only
      log(`Table ${tableName} is empty, creating CSV with headers only`);

      // Get column information
      const columnsQuery = `
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = '${tableName}'
        ORDER BY ORDINAL_POSITION
      `;

      const columnsRequest = new sql.Request(dbConnection);
      const columnsResult = await columnsRequest.query(columnsQuery);
      const columns = columnsResult.recordset.map(row => row.COLUMN_NAME);

      // Write empty row with headers
      const emptyRow = {};
      columns.forEach(col => emptyRow[col] = null);
      csvStream.write(emptyRow);

    } else {
      // Export data using streaming
      const request = new sql.Request(dbConnection);
      request.stream = true;

      let processedRows = 0;

      request.on('row', (row) => {
        csvStream.write(row);
        processedRows++;

        if (processedRows % 1000 === 0) {
          log(`Exported ${processedRows}/${rowCount} rows from ${tableName}`);
        }
      });

      request.on('error', (error) => {
        csvStream.end();
        throw new Error(`Error streaming table ${tableName}: ${error.message}`);
      });

      // Start the query
      await new Promise((resolve, reject) => {
        request.on('done', () => {
          csvStream.end();
          resolve();
        });

        request.on('error', reject);

        request.query(`SELECT * FROM [${tableName}]`);
      });

      log(`Completed exporting ${processedRows} rows from ${tableName}`);
    }

    // Wait for file to be written
    await new Promise((resolve, reject) => {
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });

    stats.totalRows += rowCount;
    stats.processedTables.push({ name: tableName, rows: rowCount });

    log(`✓ Successfully exported ${tableName} to ${csvFilePath}`);

  } catch (error) {
    log(`✗ Failed to export table ${tableName}: ${error.message}`);
    throw error;
  }
}

/**
 * Exports all discovered tables to CSV files
 * @param {string[]} tables - Array of table names
 * @returns {Promise<void>}
 */
async function exportAllTables(tables) {
  log(`Starting CSV export for ${tables.length} tables...`);
  stats.totalTables = tables.length;

  for (const tableName of tables) {
    await exportTableToCSV(tableName);
  }

  log('All tables exported successfully');
}

/**
 * Performs cleanup operations
 * @returns {Promise<void>}
 */
async function cleanup() {
  log('Performing cleanup...');

  // Close database connection
  if (dbConnection) {
    try {
      await dbConnection.close();
      log('Database connection closed');
    } catch (error) {
      log(`Warning: Error closing database connection: ${error.message}`);
    }
  }

  // Stop and remove Docker container
  if (dockerContainer) {
    try {
      const result = await executeCommand('docker', ['stop', dockerContainer]);
      if (result.code === 0) {
        log('Docker container stopped and removed');
      } else {
        log(`Warning: Error stopping container: ${result.stderr}`);
      }
    } catch (error) {
      log(`Warning: Error stopping Docker container: ${error.message}`);
    }
  }
}

/**
 * Generates and displays the final summary report
 * @returns {void}
 */
function generateSummaryReport() {
  const endTime = Date.now();
  const duration = Math.round((endTime - stats.startTime) / 1000);

  log('\n' + '='.repeat(60));
  log('DATABASE MIGRATION COMPLETE - SUMMARY REPORT');
  log('='.repeat(60));
  log(`Total tables processed: ${stats.totalTables}`);
  log(`Total rows exported: ${stats.totalRows}`);
  log(`Processing duration: ${Math.floor(duration / 60)}m ${duration % 60}s`);
  log(`Output directory: ${path.resolve(CONFIG.OUTPUT_DIR)}`);

  if (stats.processedTables.length > 0) {
    log('\nTable breakdown:');
    stats.processedTables.forEach(table => {
      log(`  ${table.name}: ${table.rows} rows`);
    });
  }

  log('='.repeat(60));
}

/**
 * Validates prerequisites before starting the migration
 * @returns {Promise<void>}
 */
async function validatePrerequisites() {
  log('Validating prerequisites...');

  // Check if backup file exists
  try {
    await fs.access(CONFIG.BACKUP_FILE);
    log(`✓ Backup file found: ${CONFIG.BACKUP_FILE}`);
  } catch (error) {
    throw new Error(`Backup file not found: ${CONFIG.BACKUP_FILE}`);
  }

  // Check if Docker is available
  try {
    const result = await executeCommand('docker', ['--version']);
    if (result.code !== 0) {
      throw new Error('Docker is not available');
    }
    log('✓ Docker is available');
  } catch (error) {
    throw new Error('Docker is not installed or not accessible');
  }

  // Create output directory
  try {
    await fs.mkdir(CONFIG.OUTPUT_DIR, { recursive: true });
    log(`✓ Output directory ready: ${CONFIG.OUTPUT_DIR}`);
  } catch (error) {
    throw new Error(`Failed to create output directory: ${error.message}`);
  }
}

/**
 * Main execution function
 * @returns {Promise<void>}
 */
async function main() {
  try {
    stats.startTime = Date.now();
    log('Starting MSSQL database migration and CSV export process');

    // Validate prerequisites
    await validatePrerequisites();

    // Start Docker container
    await startDockerContainer();

    // Wait for container to be ready
    await waitForContainerReady();

    // Restore database
    await restoreDatabase();

    // Connect to database
    await connectToDatabase();

    // Discover tables
    const tables = await discoverTables();

    // Export all tables to CSV
    await exportAllTables(tables);

    // Generate summary report
    generateSummaryReport();

    log('Migration process completed successfully');
    process.exit(0);

  } catch (error) {
    log(`Fatal error: ${error.message}`);
    console.error(error);
    process.exit(1);
  } finally {
    await cleanup();
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  log('Received SIGINT, performing cleanup and exiting...');
  await cleanup();
  process.exit(1);
});

process.on('SIGTERM', async () => {
  log('Received SIGTERM, performing cleanup and exiting...');
  await cleanup();
  process.exit(1);
});

process.on('exit', async () => {
  // Final cleanup on exit
  if (dockerContainer) {
    try {
      await executeCommand('docker', ['stop', dockerContainer]);
    } catch (error) {
      // Ignore cleanup errors on exit
    }
  }
});

// Handle unhandled promise rejections
process.on('unhandledRejection', async (reason, promise) => {
  log(`Unhandled promise rejection: ${reason}`);
  console.error(reason);
  await cleanup();
  process.exit(1);
});

// Start the process
main().catch(async (error) => {
  log(`Unhandled error in main: ${error.message}`);
  console.error(error);
  await cleanup();
  process.exit(1);
});
