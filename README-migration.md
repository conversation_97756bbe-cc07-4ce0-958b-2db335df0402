# MSSQL Docker Migration Tool

A production-ready Node.js script that automates MSSQL Server database restoration and CSV export using Docker containers.

## Features

- ✅ Automated Docker container management (MSSQL Server 2019)
- ✅ Database restoration from .bak files
- ✅ Automatic table discovery and CSV export
- ✅ Streaming data export for memory efficiency
- ✅ Comprehensive error handling and retry logic
- ✅ Graceful cleanup and signal handling
- ✅ Detailed logging with timestamps
- ✅ Progress tracking and summary reporting

## Requirements

- Node.js v20 or higher
- Docker installed and running
- MSSQL database backup file (.bak format)

## Installation

```bash
npm install
```

## Setup

1. **Place your database backup file** in the `./data/` directory:
   ```
   ./data/data.bak
   ```

2. **Ensure Docker is running** and accessible from command line:
   ```bash
   docker --version
   ```

## Usage

Run the migration script:

```bash
npm run migrate
```

or

```bash
node migrate-database.js
```

## Process Flow

1. **Validation**: Checks for backup file and Docker availability
2. **Container Startup**: Launches MSSQL Server 2019 container
3. **Readiness Check**: Polls container until database is ready (max 60s)
4. **Database Restoration**: Restores database from backup file
5. **Connection**: Connects to restored database with retry logic
6. **Table Discovery**: Finds all user tables (excludes system tables)
7. **CSV Export**: Exports each table to individual CSV files
8. **Cleanup**: Stops container and closes connections

## Output Structure

CSV files are organized as follows:
```
./output_data/
├── TableName1/
│   └── TableName1.csv
├── TableName2/
│   └── TableName2.csv
└── ...
```

## Configuration

Key settings in the script (modify as needed):

```javascript
const CONFIG = {
  CONTAINER_NAME: 'mssql-migration-temp',
  SA_PASSWORD: 'YourStrong!Passw0rd',
  DATABASE_NAME: 'MigrationDB',
  BACKUP_FILE: './data/data.bak',
  OUTPUT_DIR: './output_data',
  CONTAINER_READY_TIMEOUT: 60000,  // 60 seconds
  CONNECTION_RETRIES: 3,
  CONNECTION_RETRY_DELAY: 5000     // 5 seconds
};
```

## Error Handling

The script handles various error scenarios:
- Missing backup files
- Docker unavailability
- Container startup failures
- Database connection issues
- SQL query execution errors
- File system operations
- Memory management for large datasets

## Logging

Detailed console output includes:
- Container startup progress
- Database restoration status
- Table discovery results
- Export progress with row counts
- Error details with context
- Final summary report

## Cleanup

The script automatically:
- Stops and removes Docker containers
- Closes database connections
- Handles graceful shutdown (SIGINT/SIGTERM)
- Performs cleanup on errors

## Troubleshooting

### Common Issues

1. **"Backup file not found"**
   - Ensure `data.bak` exists in `./data/` directory
   - Check file permissions

2. **"Docker is not available"**
   - Install Docker Desktop
   - Ensure Docker daemon is running
   - Check Docker permissions

3. **"Container failed to become ready"**
   - Increase `CONTAINER_READY_TIMEOUT`
   - Check Docker resources (memory/CPU)
   - Verify backup file integrity

4. **"Database restoration failed"**
   - Check backup file format and integrity
   - Verify logical names in backup match configuration
   - Review Docker container logs: `docker logs mssql-migration-temp`

### Memory Considerations

For large databases:
- The script uses streaming to minimize memory usage
- Monitor system resources during export
- Consider processing tables individually if needed

## Security Notes

- Default SA password is used for temporary container
- Container is automatically removed after use
- No persistent data storage outside output directory
- Network access limited to localhost:1433

## Dependencies

- `mssql`: Microsoft SQL Server client
- `fast-csv`: High-performance CSV processing
- Built-in Node.js modules: `child_process`, `fs/promises`, `path`
