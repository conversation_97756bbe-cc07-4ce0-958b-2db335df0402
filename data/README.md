# Data Directory

Place your MSSQL database backup file here:

- **Required file**: `data.bak`
- **Format**: Microsoft SQL Server backup file (.bak)
- **Location**: This directory (`./data/data.bak`)

## Example

```
./data/
└── data.bak  <- Your database backup file goes here
```

The migration script will mount this directory as read-only volume in the Docker container at `/var/opt/mssql/backup/`.

## Notes

- Ensure the backup file is accessible and not corrupted
- The script expects the logical database name to be `MigrationDB` (configurable in script)
- Large backup files are supported (streaming restoration)
