# Lightspeed X-Series Image Upload Tool

A production-ready Node.js script that processes product images from an Excel file and uploads them to the Lightspeed X-Series API with robust error handling, rate limiting, and checkpoint recovery.

## Features

- ✅ Excel file processing with automatic header detection
- ✅ URL validation before download attempts
- ✅ Rate limiting (100 requests/minute)
- ✅ Retry logic with exponential backoff
- ✅ Checkpoint system for recovery on restart
- ✅ Comprehensive error handling and logging
- ✅ Progress tracking and summary reporting
- ✅ Temporary file cleanup
- ✅ Graceful shutdown handling

## Requirements

- Node.js v20 or higher
- Excel file (.xlsx) with product_id and image_url columns
- Valid Lightspeed X-Series API token
- Store domain

## Installation

```bash
npm install
```

## Configuration

Edit the CONFIG section in `upload-images.js`:

```javascript
const CONFIG = {
  EXCEL_FILE_PATH: './products_images.xlsx',
  LIGHTSPEED_API_TOKEN: 'your_actual_token_here',
  STORE_DOMAIN: 'your_store.vendhq.com',
  // ... other settings
};
```

## Excel File Format

Your Excel file should have exactly two columns:
- Column A: `product_id` (string/number)
- Column B: `image_url` (valid HTTP/HTTPS URL)

Header row is optional and will be automatically detected.

## Usage

```bash
npm start
```

or

```bash
node upload-images.js
```

## Recovery

The script automatically creates a checkpoint file (`upload_checkpoint.json`) that tracks:
- Successfully uploaded images
- Failed uploads with error details
- Processing timestamps

On restart, the script will skip already-uploaded images and continue from where it left off.

## Error Handling

The script handles various error scenarios:
- Invalid Excel file format
- Inaccessible image URLs
- API authentication failures
- Network connectivity issues
- Rate limiting (429 responses)
- Server errors (5xx responses)
- Timeouts

## Rate Limiting

- Maximum 100 API requests per minute
- Automatic retry with exponential backoff
- Concurrent request limiting

## Logging

Detailed console logging includes:
- Timestamps for all operations
- Progress indicators
- Error details with retry attempts
- Final summary report

## Output

Upon completion, you'll see a comprehensive summary:
- Total products/images processed
- Success/failure counts
- Processing duration
- Breakdown of failure reasons