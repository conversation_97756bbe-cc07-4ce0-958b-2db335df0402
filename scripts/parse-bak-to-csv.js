#!/usr/bin/env node

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import sql from 'mssql';
import csv from 'fast-csv';

// Configuration constants
const CONFIG = {
  CONTAINER_NAME: 'mssql-migration-temp',
  DOCKER_IMAGE: 'mcr.microsoft.com/mssql/server:2019-latest',
  SA_PASSWORD: 'YourStrong!Passw0rd123',  // Enhanced password for MSSQL complexity requirements
  DATABASE_NAME: 'MigrationDB',
  BACKUP_FILE: './input_data/retailer.bak',
  OUTPUT_DIR: './output_data',
  CONTAINER_READY_TIMEOUT: 120000, // 120 seconds (increased for slower systems)
  CONTAINER_READY_INTERVAL: 3000,  // 3 seconds (reduced frequency)
  CONNECTION_TIMEOUT: 30000,       // 30 seconds
  REQUEST_TIMEOUT: 60000,          // 60 seconds
  CONNECTION_RETRIES: 3,
  CONNECTION_RETRY_DELAY: 5000     // 5 seconds
};

// Global state
let dockerContainer = null;
let dbConnection = null;
let stats = {
  totalTables: 0,
  totalRows: 0,
  startTime: null,
  processedTables: []
};

/**
 * Logs a message with timestamp
 * @param {string} message - The message to log
 */
function log(message) {
  console.log(`[${new Date().toISOString()}] ${message}`);
}

/**
 * Executes a shell command using spawn and returns a promise
 * @param {string} command - The command to execute
 * @param {string[]} args - Command arguments
 * @param {Object} options - Spawn options
 * @returns {Promise<{stdout: string, stderr: string, code: number}>}
 */
function executeCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, { 
      stdio: ['pipe', 'pipe', 'pipe'],
      ...options 
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout?.on('data', (data) => {
      stdout += data.toString();
    });
    
    child.stderr?.on('data', (data) => {
      stderr += data.toString();
    });
    
    child.on('close', (code) => {
      resolve({ stdout, stderr, code });
    });
    
    child.on('error', (error) => {
      reject(new Error(`Failed to execute ${command}: ${error.message}`));
    });
  });
}

/**
 * Starts the MSSQL Docker container
 * @returns {Promise<void>}
 */
async function startDockerContainer() {
  log('Starting MSSQL Docker container...');
  
  // Check if container already exists and remove it
  try {
    const { code } = await executeCommand('docker', ['rm', '-f', CONFIG.CONTAINER_NAME]);
    if (code === 0) {
      log('Removed existing container');
    }
  } catch (error) {
    // Container doesn't exist, continue
  }
  
  const dockerArgs = [
    'run',
    '--name', CONFIG.CONTAINER_NAME,
    '--rm',
    '-p', '1433:1433',
    '-e', 'ACCEPT_EULA=Y',
    '-e', `SA_PASSWORD=${CONFIG.SA_PASSWORD}`,
    '-e', 'MSSQL_PID=Express',  // Use Express edition for better compatibility
    '--memory=2g',              // Ensure minimum 2GB memory for MSSQL
    '--cpus=2',                 // Allocate 2 CPUs for better performance
    '-v', `${path.resolve('./input_data')}:/var/opt/mssql/backup:ro`,
    '-d',
    CONFIG.DOCKER_IMAGE
  ];
  
  const result = await executeCommand('docker', dockerArgs);
  
  if (result.code !== 0) {
    throw new Error(`Failed to start Docker container: ${result.stderr}`);
  }
  
  dockerContainer = CONFIG.CONTAINER_NAME;
  log(`Docker container started: ${dockerContainer}`);
}

/**
 * Gets Docker container logs for diagnostics
 * @returns {Promise<string>}
 */
async function getContainerLogs() {
  try {
    const result = await executeCommand('docker', ['logs', CONFIG.CONTAINER_NAME]);
    return result.stdout + result.stderr;
  } catch (error) {
    return `Failed to get container logs: ${error.message}`;
  }
}

/**
 * Checks container status and resource usage
 * @returns {Promise<string>}
 */
async function getContainerStatus() {
  try {
    const result = await executeCommand('docker', [
      'inspect',
      CONFIG.CONTAINER_NAME,
      '--format', '{{.State.Status}} | {{.State.Health.Status}} | Memory: {{.HostConfig.Memory}} | CPUs: {{.HostConfig.CpuCount}}'
    ]);
    return result.stdout.trim();
  } catch (error) {
    return `Failed to get container status: ${error.message}`;
  }
}

/**
 * Waits for the MSSQL container to be ready
 * @returns {Promise<void>}
 */
async function waitForContainerReady() {
  log('Waiting for MSSQL container to be ready...');
  log(`Timeout: ${CONFIG.CONTAINER_READY_TIMEOUT / 1000}s, Check interval: ${CONFIG.CONTAINER_READY_INTERVAL / 1000}s`);

  const startTime = Date.now();
  const timeout = CONFIG.CONTAINER_READY_TIMEOUT;
  let attemptCount = 0;

  while (Date.now() - startTime < timeout) {
    attemptCount++;
    const elapsed = Math.round((Date.now() - startTime) / 1000);

    try {
      // Check container status first
      const containerStatus = await getContainerStatus();
      log(`Attempt ${attemptCount} (${elapsed}s): Container status - ${containerStatus}`);

      // Try to connect with sqlcmd (using newer mssql-tools18 path and trust certificate)
      const result = await executeCommand('docker', [
        'exec',
        CONFIG.CONTAINER_NAME,
        '/opt/mssql-tools18/bin/sqlcmd',
        '-S', 'localhost',
        '-U', 'sa',
        '-P', CONFIG.SA_PASSWORD,
        '-C',           // Trust server certificate
        '-Q', 'SELECT 1',
        '-t', '10'      // 10 second timeout for sqlcmd
      ]);

      if (result.code === 0) {
        log(`✓ MSSQL container is ready after ${elapsed}s (${attemptCount} attempts)`);
        return;
      } else {
        log(`sqlcmd failed with code ${result.code}: ${result.stderr.trim()}`);
      }

    } catch (error) {
      log(`Connection attempt ${attemptCount} failed: ${error.message}`);
    }

    // Show progress every 30 seconds
    if (elapsed > 0 && elapsed % 30 === 0) {
      log(`Still waiting... ${elapsed}s elapsed, ${Math.round((timeout - (Date.now() - startTime)) / 1000)}s remaining`);

      // Get container logs for diagnostics
      const logs = await getContainerLogs();
      if (logs) {
        log('Recent container logs:');
        console.log(logs.split('\n').slice(-10).join('\n')); // Show last 10 lines
      }
    }

    await new Promise(resolve => setTimeout(resolve, CONFIG.CONTAINER_READY_INTERVAL));
  }

  // Final diagnostics before failing
  log('Container readiness check timed out. Final diagnostics:');
  const finalStatus = await getContainerStatus();
  const finalLogs = await getContainerLogs();

  log(`Final container status: ${finalStatus}`);
  log('Final container logs:');
  console.log(finalLogs);

  throw new Error(`Container failed to become ready within ${timeout / 1000} seconds. Check logs above for details.`);
}

/**
 * Gets backup file information
 * @returns {Promise<void>}
 */
async function getBackupFileInfo() {
  log('Getting backup file information...');

  const backupFileName = path.basename(CONFIG.BACKUP_FILE);
  const infoQuery = `RESTORE FILELISTONLY FROM DISK = '/var/opt/mssql/backup/${backupFileName}'`;

  const result = await executeCommand('docker', [
    'exec',
    CONFIG.CONTAINER_NAME,
    '/opt/mssql-tools18/bin/sqlcmd',
    '-S', 'localhost',
    '-U', 'sa',
    '-P', CONFIG.SA_PASSWORD,
    '-C',       // Trust server certificate
    '-Q', infoQuery,
    '-h', '-1'  // Remove headers for cleaner output
  ]);

  if (result.code === 0) {
    log('Backup file contents:');
    console.log(result.stdout);
    return result.stdout;
  } else {
    log(`Warning: Could not read backup file info: ${result.stderr}`);
    return null;
  }
}

/**
 * Restores the database from backup file
 * @returns {Promise<void>}
 */
async function restoreDatabase() {
  log('Restoring database from backup...');

  const backupFileName = path.basename(CONFIG.BACKUP_FILE);
  log(`Using backup file: ${backupFileName}`);

  // Get backup file information first
  await getBackupFileInfo();

  // Use explicit file move approach based on backup file info
  const explicitRestoreQuery = `
    RESTORE DATABASE ${CONFIG.DATABASE_NAME}
    FROM DISK = '/var/opt/mssql/backup/${backupFileName}'
    WITH REPLACE,
    MOVE 'QSDB_Data' TO '/var/opt/mssql/data/${CONFIG.DATABASE_NAME}.mdf',
    MOVE 'QSDB_Log' TO '/var/opt/mssql/data/${CONFIG.DATABASE_NAME}_Log.ldf'
  `;

  log('Attempting database restoration...');

  let result = await executeCommand('docker', [
    'exec',
    CONFIG.CONTAINER_NAME,
    '/opt/mssql-tools18/bin/sqlcmd',
    '-S', 'localhost',
    '-U', 'sa',
    '-P', CONFIG.SA_PASSWORD,
    '-C',        // Trust server certificate
    '-Q', explicitRestoreQuery,
    '-t', '300'  // 5 minute timeout for restore operation
  ]);

  log(`Restore command output: ${result.stdout}`);
  log(`Restore command errors: ${result.stderr}`);

  if (result.code !== 0) {
    log(`Simple restore failed, trying with file relocation...`);
    log(`Error: ${result.stderr}`);

    // If simple restore fails, try with explicit file names from backup info
    const explicitRestoreQuery = `
      RESTORE DATABASE ${CONFIG.DATABASE_NAME}
      FROM DISK = '/var/opt/mssql/backup/${backupFileName}'
      WITH REPLACE,
      MOVE 'QSDB_Data' TO '/var/opt/mssql/data/${CONFIG.DATABASE_NAME}.mdf',
      MOVE 'QSDB_Log' TO '/var/opt/mssql/data/${CONFIG.DATABASE_NAME}_Log.ldf'
    `;

    result = await executeCommand('docker', [
      'exec',
      CONFIG.CONTAINER_NAME,
      '/opt/mssql-tools18/bin/sqlcmd',
      '-S', 'localhost',
      '-U', 'sa',
      '-P', CONFIG.SA_PASSWORD,
      '-C',        // Trust server certificate
      '-Q', explicitRestoreQuery,
      '-t', '300'
    ]);
  }

  if (result.code !== 0) {
    log('Database restoration failed. Container logs:');
    const logs = await getContainerLogs();
    console.log(logs);
    throw new Error(`Database restoration failed: ${result.stderr}`);
  }

  // Check if there was an error message in the output even if the code was 0
  if (result.stderr && result.stderr.includes('Error:')) {
    log(`Warning: Database restoration reported success but had errors: ${result.stderr}`);

    // Try to get more detailed error information
    const errorQuery = `
      SELECT TOP 5 [Text]
      FROM sys.dm_os_ring_buffers
      WHERE ring_buffer_type = 'RING_BUFFER_LOGGING'
      ORDER BY timestamp DESC
    `;

    const errorResult = await executeCommand('docker', [
      'exec',
      CONFIG.CONTAINER_NAME,
      '/opt/mssql-tools18/bin/sqlcmd',
      '-S', 'localhost',
      '-U', 'sa',
      '-P', CONFIG.SA_PASSWORD,
      '-C',
      '-Q', errorQuery
    ]);

    if (errorResult.code === 0) {
      log('Recent SQL Server errors:');
      console.log(errorResult.stdout);
    }
  } else {
    log('✓ Database restored successfully');
  }

  // Check what databases exist after restoration
  const listDbResult = await executeCommand('docker', [
    'exec',
    CONFIG.CONTAINER_NAME,
    '/opt/mssql-tools18/bin/sqlcmd',
    '-S', 'localhost',
    '-U', 'sa',
    '-P', CONFIG.SA_PASSWORD,
    '-C',        // Trust server certificate
    '-Q', 'SELECT name FROM sys.databases',
    '-h', '-1'   // Remove headers
  ]);

  if (listDbResult.code === 0) {
    const allDatabases = listDbResult.stdout.trim().split('\n').filter(db => db.trim() && !db.includes('rows affected'));
    const userDatabases = allDatabases.filter(db => !['master', 'tempdb', 'model', 'msdb'].includes(db.trim()));

    log(`All databases: ${allDatabases.join(', ')}`);
    log(`User databases: ${userDatabases.join(', ')}`);

    if (userDatabases.length > 0) {
      // Use the first available database if MigrationDB doesn't exist
      const actualDbName = userDatabases.includes(CONFIG.DATABASE_NAME) ? CONFIG.DATABASE_NAME : userDatabases[0];
      if (actualDbName !== CONFIG.DATABASE_NAME) {
        log(`Using database '${actualDbName}' instead of '${CONFIG.DATABASE_NAME}'`);
        // Update the config to use the actual database name
        CONFIG.DATABASE_NAME = actualDbName;
      }

      // Verify database is accessible
      const verifyResult = await executeCommand('docker', [
        'exec',
        CONFIG.CONTAINER_NAME,
        '/opt/mssql-tools18/bin/sqlcmd',
        '-S', 'localhost',
        '-U', 'sa',
        '-P', CONFIG.SA_PASSWORD,
        '-C',        // Trust server certificate
        '-d', actualDbName,
        '-Q', 'SELECT DB_NAME() as CurrentDatabase'
      ]);

      if (verifyResult.code === 0) {
        log('✓ Database verification successful');
      } else {
        log(`Warning: Database verification failed: ${verifyResult.stderr}`);
      }
    } else {
      log('Warning: No user databases found after restoration');
    }
  } else {
    log(`Warning: Could not list databases: ${listDbResult.stderr}`);
  }
}

/**
 * Establishes connection to the MSSQL database with retry logic
 * @returns {Promise<void>}
 */
async function connectToDatabase() {
  const config = {
    server: 'localhost',
    port: 1433,
    database: CONFIG.DATABASE_NAME,
    user: 'sa',
    password: CONFIG.SA_PASSWORD,
    options: {
      trustServerCertificate: true,
      connectTimeout: CONFIG.CONNECTION_TIMEOUT,
      requestTimeout: CONFIG.REQUEST_TIMEOUT
    }
  };
  
  let lastError = null;
  
  for (let attempt = 1; attempt <= CONFIG.CONNECTION_RETRIES; attempt++) {
    try {
      log(`Connecting to database (attempt ${attempt}/${CONFIG.CONNECTION_RETRIES})...`);
      
      dbConnection = new sql.ConnectionPool(config);
      await dbConnection.connect();
      
      log('Successfully connected to database');
      return;
      
    } catch (error) {
      lastError = error;
      log(`Connection attempt ${attempt} failed: ${error.message}`);
      
      if (attempt < CONFIG.CONNECTION_RETRIES) {
        log(`Retrying in ${CONFIG.CONNECTION_RETRY_DELAY / 1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, CONFIG.CONNECTION_RETRY_DELAY));
      }
    }
  }
  
  throw new Error(`Failed to connect to database after ${CONFIG.CONNECTION_RETRIES} attempts: ${lastError?.message}`);
}

/**
 * Discovers all user tables in the database
 * @returns {Promise<string[]>} - Array of table names
 */
async function discoverTables() {
  log('Discovering user tables...');

  const query = `
    SELECT TABLE_NAME
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_SCHEMA != 'sys'
    ORDER BY TABLE_NAME
  `;

  const request = new sql.Request(dbConnection);
  const result = await request.query(query);

  const tables = result.recordset.map(row => row.TABLE_NAME);
  log(`Found ${tables.length} user tables: ${tables.join(', ')}`);

  return tables;
}

/**
 * Gets the row count for a specific table
 * @param {string} tableName - The table name
 * @returns {Promise<number>} - Row count
 */
async function getTableRowCount(tableName) {
  try {
    const request = new sql.Request(dbConnection);
    const result = await request.query(`SELECT COUNT(*) as row_count FROM [${tableName}]`);
    return result.recordset[0].row_count;
  } catch (error) {
    log(`Error getting row count for table ${tableName}: ${error.message}`);
    return 0;
  }
}

/**
 * Gets detailed table information including row counts from system tables
 * @returns {Promise<void>}
 */
async function getDetailedTableInfo() {
  try {
    const request = new sql.Request(dbConnection);
    const result = await request.query(`
      SELECT
        t.name as table_name,
        p.rows as row_count,
        CAST(ROUND(((SUM(a.total_pages) * 8) / 1024.00), 2) AS NUMERIC(36, 2)) AS size_mb
      FROM sys.tables t
      INNER JOIN sys.indexes i ON t.OBJECT_ID = i.object_id
      INNER JOIN sys.partitions p ON i.object_id = p.OBJECT_ID AND i.index_id = p.index_id
      INNER JOIN sys.allocation_units a ON p.partition_id = a.container_id
      LEFT OUTER JOIN sys.schemas s ON t.schema_id = s.schema_id
      WHERE t.NAME NOT LIKE 'dt%'
        AND t.is_ms_shipped = 0
        AND i.OBJECT_ID > 255
        AND i.index_id <= 1
      GROUP BY t.name, p.rows
      ORDER BY p.rows DESC
    `);

    log('Top 20 tables by row count (from system tables):');
    result.recordset.slice(0, 20).forEach(row => {
      log(`  ${row.table_name}: ${row.row_count} rows (${row.size_mb} MB)`);
    });

    const totalRows = result.recordset.reduce((sum, row) => sum + parseInt(row.row_count), 0);
    log(`Total rows across all tables: ${totalRows.toLocaleString()}`);

    return result.recordset;
  } catch (error) {
    log(`Error getting detailed table info: ${error.message}`);
    return [];
  }
}

/**
 * Exports a single table to CSV
 * @param {string} tableName - The table name to export
 * @param {number} systemRowCount - Row count from system tables (optional)
 * @returns {Promise<void>}
 */
async function exportTableToCSV(tableName, systemRowCount = null) {
  log(`Processing table: ${tableName}`);

  try {
    // Use system row count if provided, otherwise query for it
    const rowCount = systemRowCount !== null ? systemRowCount : await getTableRowCount(tableName);
    log(`Table ${tableName}: ${rowCount.toLocaleString()} rows`);

    // Create output directory
    const outputDir = path.join(CONFIG.OUTPUT_DIR, tableName);
    await fs.mkdir(outputDir, { recursive: true });

    const csvFilePath = path.join(outputDir, `${tableName}.csv`);

    // Create CSV write stream
    const csvStream = csv.format({ headers: true, encoding: 'utf8' });
    const writeStream = (await import('fs')).createWriteStream(csvFilePath, { encoding: 'utf8' });
    csvStream.pipe(writeStream);

    if (rowCount === 0) {
      // Handle empty table - create CSV with headers only
      log(`Table ${tableName} is empty, creating CSV with headers only`);

      // Get column information
      const columnsQuery = `
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = '${tableName}'
        ORDER BY ORDINAL_POSITION
      `;

      const columnsRequest = new sql.Request(dbConnection);
      const columnsResult = await columnsRequest.query(columnsQuery);
      const columns = columnsResult.recordset.map(row => row.COLUMN_NAME);

      // Write empty row with headers
      const emptyRow = {};
      columns.forEach(col => emptyRow[col] = null);
      csvStream.write(emptyRow);

    } else {
      // Export data using streaming
      const request = new sql.Request(dbConnection);
      request.stream = true;
      request.timeout = 300000; // 5 minute timeout for large tables

      let processedRows = 0;

      // Test query first to see if we can get data (with timeout)
      const testRequest = new sql.Request(dbConnection);
      testRequest.timeout = 30000; // 30 second timeout
      const testResult = await testRequest.query(`SELECT TOP 5 * FROM [${tableName}]`);
      log(`Test query for ${tableName} returned ${testResult.recordset.length} rows`);
      if (testResult.recordset.length > 0) {
        log(`Sample data: ${JSON.stringify(testResult.recordset[0]).substring(0, 200)}...`);
      }

      request.on('row', (row) => {
        csvStream.write(row);
        processedRows++;

        // Log first few rows for debugging
        if (processedRows <= 3) {
          log(`Sample row ${processedRows} from ${tableName}: ${JSON.stringify(row).substring(0, 200)}...`);
        }

        if (processedRows % 1000 === 0) {
          log(`Exported ${processedRows}/${rowCount} rows from ${tableName}`);
        }
      });

      request.on('error', (error) => {
        csvStream.end();
        throw new Error(`Error streaming table ${tableName}: ${error.message}`);
      });

      // Start the query
      await new Promise((resolve, reject) => {
        request.on('done', () => {
          csvStream.end();
          resolve();
        });

        request.on('error', reject);

        request.query(`SELECT * FROM [${tableName}]`);
      });

      log(`Completed exporting ${processedRows} rows from ${tableName}`);
    }

    // Wait for file to be written
    await new Promise((resolve, reject) => {
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });

    stats.totalRows += rowCount;
    stats.processedTables.push({ name: tableName, rows: rowCount });

    log(`✓ Successfully exported ${tableName} to ${csvFilePath}`);

  } catch (error) {
    log(`✗ Failed to export table ${tableName}: ${error.message}`);
    throw error;
  }
}

/**
 * Exports all discovered tables to CSV files, prioritizing by row count
 * @param {string[]} tables - Array of table names
 * @returns {Promise<void>}
 */
async function exportAllTables(tables) {
  log(`Starting CSV export for ${tables.length} tables...`);
  stats.totalTables = tables.length;

  // Get detailed table info with row counts
  const tableInfo = await getDetailedTableInfo();

  // Create a map of table names to row counts
  const tableRowCounts = new Map();
  tableInfo.forEach(info => {
    tableRowCounts.set(info.table_name, parseInt(info.row_count));
  });

  // Sort tables by row count (largest first) to process important data first
  const sortedTables = tables.sort((a, b) => {
    const rowsA = tableRowCounts.get(a) || 0;
    const rowsB = tableRowCounts.get(b) || 0;
    return rowsB - rowsA;
  });

  log(`Processing tables in order of size (largest first):`);
  sortedTables.slice(0, 10).forEach(tableName => {
    const rows = tableRowCounts.get(tableName) || 0;
    log(`  ${tableName}: ${rows.toLocaleString()} rows`);
  });

  for (const tableName of sortedTables) {
    const systemRowCount = tableRowCounts.get(tableName) || 0;
    await exportTableToCSV(tableName, systemRowCount);
  }

  log('All tables exported successfully');
}

/**
 * Performs cleanup operations
 * @returns {Promise<void>}
 */
async function cleanup() {
  log('Performing cleanup...');

  // Close database connection
  if (dbConnection) {
    try {
      await dbConnection.close();
      log('Database connection closed');
    } catch (error) {
      log(`Warning: Error closing database connection: ${error.message}`);
    }
  }

  // Stop and remove Docker container
  if (dockerContainer) {
    console.log("Not stopping docker container...");
    // try {
    //   const result = await executeCommand('docker', ['stop', dockerContainer]);
    //   if (result.code === 0) {
    //     log('Docker container stopped and removed');
    //   } else {
    //     log(`Warning: Error stopping container: ${result.stderr}`);
    //   }
    // } catch (error) {
    //   log(`Warning: Error stopping Docker container: ${error.message}`);
    // }
  }
}

/**
 * Generates and displays the final summary report
 * @returns {void}
 */
function generateSummaryReport() {
  const endTime = Date.now();
  const duration = Math.round((endTime - stats.startTime) / 1000);

  log('\n' + '='.repeat(60));
  log('DATABASE MIGRATION COMPLETE - SUMMARY REPORT');
  log('='.repeat(60));
  log(`Total tables processed: ${stats.totalTables}`);
  log(`Total rows exported: ${stats.totalRows}`);
  log(`Processing duration: ${Math.floor(duration / 60)}m ${duration % 60}s`);
  log(`Output directory: ${path.resolve(CONFIG.OUTPUT_DIR)}`);

  if (stats.processedTables.length > 0) {
    log('\nTable breakdown:');
    stats.processedTables.forEach(table => {
      log(`  ${table.name}: ${table.rows} rows`);
    });
  }

  log('='.repeat(60));
}

/**
 * Validates prerequisites before starting the migration
 * @returns {Promise<void>}
 */
async function validatePrerequisites() {
  log('Validating prerequisites...');

  // Check if backup file exists
  try {
    await fs.access(CONFIG.BACKUP_FILE);
    log(`✓ Backup file found: ${CONFIG.BACKUP_FILE}`);
  } catch (error) {
    throw new Error(`Backup file not found: ${CONFIG.BACKUP_FILE}`);
  }

  // Check if Docker is available
  try {
    const result = await executeCommand('docker', ['--version']);
    if (result.code !== 0) {
      throw new Error('Docker is not available');
    }
    log('✓ Docker is available');
  } catch (error) {
    throw new Error('Docker is not installed or not accessible');
  }

  // Create output directory
  try {
    await fs.mkdir(CONFIG.OUTPUT_DIR, { recursive: true });
    log(`✓ Output directory ready: ${CONFIG.OUTPUT_DIR}`);
  } catch (error) {
    throw new Error(`Failed to create output directory: ${error.message}`);
  }
}

/**
 * Main execution function
 * @returns {Promise<void>}
 */
async function main() {
  try {
    stats.startTime = Date.now();
    log('Starting MSSQL database migration and CSV export process');

    // Validate prerequisites
    await validatePrerequisites();

    // Start Docker container
    await startDockerContainer();

    // Wait for container to be ready
    await waitForContainerReady();

    // Restore database
    await restoreDatabase();

    // Connect to database
    await connectToDatabase();

    // Discover tables
    const tables = await discoverTables();

    // Export all tables to CSV (this will get detailed table info internally)
    await exportAllTables(tables);

    // Generate summary report
    generateSummaryReport();

    log('Migration process completed successfully');
    process.exit(0);

  } catch (error) {
    log(`Fatal error: ${error.message}`);
    console.error(error);
    process.exit(1);
  } finally {
    await cleanup();
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  log('Received SIGINT, performing cleanup and exiting...');
  await cleanup();
  process.exit(1);
});

process.on('SIGTERM', async () => {
  log('Received SIGTERM, performing cleanup and exiting...');
  await cleanup();
  process.exit(1);
});

process.on('exit', async () => {
  // Final cleanup on exit
  if (dockerContainer) {
    try {
      await executeCommand('docker', ['stop', dockerContainer]);
    } catch (error) {
      // Ignore cleanup errors on exit
    }
  }
});

// Handle unhandled promise rejections
process.on('unhandledRejection', async (reason) => {
  log(`Unhandled promise rejection: ${reason}`);
  console.error(reason);
  await cleanup();
  process.exit(1);
});

// Start the process
main().catch(async (error) => {
  log(`Unhandled error in main: ${error.message}`);
  console.error(error);
  await cleanup();
  process.exit(1);
});
